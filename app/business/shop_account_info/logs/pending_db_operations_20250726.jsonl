{"timestamp": "2025-07-26T11:03:29.619209", "operation": "update", "table": "account_trade_detail", "sql": "\n                UPDATE account_trade_detail \n                SET order_no = 'FXAM250528021414', shop = 'yayin1974US(yayiUS)', flow_status = 2, execution_log = '[2025-07-26 11:03:29] rpa-task-shop_account_info_shop_account_processor_async_20250726_110200_816: [数据保存] 成功获取店铺账号: yayin1974US(yayiUS)', log_categories = 'SUCCESS'\n                WHERE id = debug_001\n                ", "data": {"account_trade_detail_id": "debug_001", "order_number": "FXAM250528021414", "shop_account": "yayin1974US(yayiUS)", "flow_status": 2}, "status": "pending"}
{"timestamp": "2025-07-26T11:26:23.018128", "operation": "update", "table": "account_trade_detail", "sql": "\n                UPDATE account_trade_detail \n                SET order_no = 'FXAM250528021414', shop = 'yayin1974US(yayiUS)', flow_status = 2, execution_log = '[2025-07-26 11:26:22] rpa-task-shop_account_info_shop_account_processor_async_20250726_112458_297: [数据保存] 成功获取店铺账号: yayin1974US(yayiUS)', log_categories = 'SUCCESS'\n                WHERE id = debug_001\n                ", "data": {"account_trade_detail_id": "debug_001", "order_number": "FXAM250528021414", "shop_account": "yayin1974US(yayiUS)", "flow_status": 2}, "status": "pending"}
